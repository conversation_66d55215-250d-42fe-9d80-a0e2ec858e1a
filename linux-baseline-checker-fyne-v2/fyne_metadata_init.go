package main

import (
	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/app"
)

func init() {
	app.SetMetadata(fyne.AppMetadata{
		ID: "",
		Name: "Linux基线检查工具",
		Version: "0.0.1",
		Build: 1,
		Icon: &fyne.StaticResource{
	StaticName: "Icon.png",
	StaticContent: []byte{
		137, 80, 78, 71, 13, 10, 26, 10, 0, 0, 0, 13, 73, 72, 68, 82, 0, 0, 2, 0, 0, 0, 2, 0, 4, 3, 0, 0, 0, 6, 86, 201, 201, 0, 0, 0, 48, 80, 76, 84, 69, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 90, 63, 28, 11, 0, 0, 0, 15, 116, 82, 78, 83, 0, 51, 119, 127, 128, 129, 130, 131, 132, 136, 137, 138, 139, 140, 153, 201, 85, 239, 252, 0, 0, 5, 91, 73, 68, 65, 84, 120, 218, 237, 221, 193, 177, 28, 53, 24, 69, 225, 11, 5, 51, 44, 157, 9, 206, 132, 16, 8, 65, 25, 160, 12, 240, 132, 208, 33, 116, 8, 19, 130, 3, 129, 98, 225, 85, 183, 100, 154, 8, 88, 80, 197, 232, 252, 239, 249, 124, 33, 220, 58, 171, 105, 141, 20, 73, 146, 36, 73, 146, 36, 73, 146, 36, 73, 146, 36, 73, 146, 36, 73, 146, 36, 73, 146, 36, 73, 146, 36, 73, 146, 36, 73, 146, 36, 73, 146, 36, 73, 146, 36, 73, 146, 36, 73, 146, 36, 73, 146, 36, 189, 83, 223, 229, 197, 62, 164, 182, 223, 243, 98, 127, 166, 180, 159, 254, 206, 139, 253, 245, 57, 149, 125, 122, 253, 0, 95, 75, 7, 112, 189, 126, 128, 235, 115, 229, 0, 86, 12, 240, 181, 114, 0, 43, 6, 40, 156, 192, 167, 53, 3, 124, 173, 27, 192, 146, 1, 234, 38, 208, 87, 13, 48, 83, 210, 253, 90, 53, 192, 245, 76, 69, 125, 221, 0, 179, 102, 0, 139, 6, 168, 154, 64, 95, 57, 192, 172, 24, 192, 170, 1, 138, 38, 208, 215, 14, 48, 235, 5, 176, 108, 128, 154, 9, 244, 213, 3, 204, 106, 1, 172, 27, 160, 100, 2, 125, 253, 0, 179, 86, 0, 11, 7, 168, 152, 64, 39, 6, 152, 149, 2, 0, 6, 168, 148, 64, 103, 6, 152, 117, 2, 0, 6, 168, 148, 64, 167, 6, 152, 85, 2, 0, 6, 168, 148, 64, 231, 6, 152, 53, 2, 0, 6, 168, 148, 64, 39, 7, 152, 21, 2, 0, 6, 168, 148, 64, 103, 7, 152, 124, 0, 232, 0, 124, 2, 157, 30, 96, 210, 1, 160, 3, 240, 9, 116, 126, 128, 201, 6, 128, 14, 192, 39, 208, 43, 12, 48, 201, 0, 208, 1, 248, 4, 122, 141, 1, 38, 23, 0, 58, 0, 159, 64, 175, 50, 192, 164, 2, 64, 7, 224, 19, 104, 117, 6, 24, 1, 220, 174, 58, 3, 92, 59, 27, 0, 63, 192, 32, 2, 64, 7, 224, 19, 104, 181, 6, 24, 64, 0, 228, 0, 124, 2, 173, 218, 0, 3, 8, 128, 27, 128, 79, 160, 213, 27, 96, 0, 1, 80, 3, 240, 9, 180, 138, 3, 12, 32, 0, 102, 0, 62, 129, 86, 115, 128, 1, 4, 64, 12, 192, 39, 208, 170, 14, 48, 128, 0, 128, 1, 240, 4, 90, 221, 1, 6, 16, 0, 48, 0, 156, 64, 171, 60, 192, 0, 2, 0, 6, 64, 19, 104, 181, 7, 24, 64, 0, 192, 0, 96, 2, 173, 250, 0, 3, 8, 0, 24, 0, 75, 160, 213, 31, 96, 0, 1, 0, 3, 64, 9, 180, 183, 48, 192, 0, 2, 0, 6, 64, 18, 104, 111, 99, 128, 1, 4, 0, 12, 0, 36, 208, 222, 202, 0, 99, 65, 0, 224, 0, 88, 2, 173, 200, 0, 84, 2, 63, 94, 111, 103, 128, 107, 203, 255, 239, 215, 183, 52, 192, 9, 4, 80, 106, 128, 107, 3, 2, 40, 229, 4, 2, 168, 101, 99, 3, 224, 157, 112, 0, 188, 141, 13, 128, 119, 194, 1, 240, 54, 54, 0, 222, 9, 7, 192, 219, 216, 0, 120, 39, 28, 0, 111, 99, 3, 224, 157, 112, 0, 188, 141, 13, 128, 119, 194, 1, 240, 54, 54, 0, 222, 9, 7, 192, 219, 216, 0, 120, 39, 28, 0, 111, 99, 3, 224, 157, 112, 0, 188, 141, 13, 128, 119, 194, 1, 240, 54, 54, 0, 222, 9, 7, 192, 219, 216, 0, 120, 39, 28, 0, 111, 99, 3, 224, 157, 112, 0, 188, 141, 13, 128, 119, 22, 12, 160, 126, 2, 191, 92, 239, 200, 145, 255, 236, 135, 235, 93, 121, 176, 1, 240, 14, 56, 0, 222, 131, 13, 128, 119, 192, 1, 240, 30, 108, 0, 188, 3, 14, 128, 247, 96, 3, 224, 29, 112, 0, 188, 7, 27, 0, 239, 128, 3, 224, 61, 216, 0, 120, 7, 28, 0, 239, 193, 6, 192, 59, 224, 0, 120, 15, 54, 0, 222, 1, 7, 192, 123, 176, 1, 240, 14, 56, 0, 222, 131, 13, 128, 119, 192, 1, 240, 30, 108, 0, 188, 3, 14, 128, 247, 224, 2, 168, 159, 192, 207, 215, 55, 224, 75, 254, 221, 247, 215, 55, 224, 99, 224, 4, 248, 0, 224, 4, 248, 0, 216, 4, 248, 0, 224, 4, 248, 0, 216, 4, 248, 0, 200, 4, 120, 31, 209, 31, 68, 120, 95, 252, 73, 204, 31, 69, 253, 89, 220, 15, 35, 126, 26, 243, 227, 168, 159, 199, 61, 32, 225, 17, 25, 15, 73, 121, 76, 206, 131, 146, 30, 149, 245, 176, 180, 199, 229, 253, 195, 4, 144, 0, 21, 0, 155, 0, 31, 0, 156, 0, 31, 0, 155, 0, 31, 0, 156, 0, 31, 0, 155, 0, 31, 0, 156, 0, 31, 0, 155, 0, 31, 0, 156, 0, 31, 0, 155, 0, 31, 0, 156, 0, 31, 0, 155, 0, 31, 0, 156, 0, 31, 0, 155, 0, 31, 0, 156, 0, 31, 0, 152, 0, 239, 240, 74, 77, 47, 85, 245, 90, 93, 47, 86, 246, 106, 109, 47, 87, 247, 122, 125, 31, 88, 240, 137, 13, 31, 89, 241, 153, 29, 31, 90, 242, 169, 45, 31, 91, 243, 185, 61, 31, 92, 244, 201, 205, 23, 36, 80, 112, 128, 51, 1, 18, 40, 52, 192, 150, 0, 9, 212, 25, 224, 76, 128, 4, 10, 13, 176, 37, 64, 2, 117, 6, 56, 19, 32, 129, 66, 3, 108, 9, 144, 64, 157, 1, 206, 4, 72, 160, 208, 0, 91, 2, 38, 192, 15, 112, 38, 100, 2, 252, 0, 91, 2, 38, 192, 15, 112, 38, 100, 2, 252, 0, 91, 2, 38, 192, 15, 112, 38, 100, 2, 252, 0, 91, 2, 38, 192, 15, 112, 38, 100, 2, 252, 0, 91, 2, 38, 192, 15, 112, 102, 133, 91, 221, 1, 246, 44, 209, 170, 14, 48, 18, 50, 1, 126, 128, 61, 139, 180, 154, 3, 140, 132, 76, 128, 31, 96, 207, 50, 173, 226, 0, 35, 33, 19, 224, 7, 216, 179, 80, 171, 55, 192, 72, 200, 4, 248, 1, 246, 44, 213, 170, 13, 48, 18, 50, 1, 126, 128, 61, 139, 181, 90, 3, 140, 132, 76, 128, 31, 96, 207, 114, 173, 210, 0, 35, 33, 19, 224, 7, 216, 3, 104, 117, 6, 24, 33, 220, 234, 12, 176, 7, 209, 170, 12, 48, 194, 184, 85, 25, 96, 15, 164, 213, 24, 96, 132, 114, 171, 49, 192, 30, 76, 171, 48, 192, 8, 231, 86, 97, 128, 61, 160, 198, 15, 48, 66, 186, 241, 3, 236, 65, 53, 122, 128, 17, 214, 141, 30, 96, 15, 172, 177, 3, 140, 208, 110, 236, 0, 123, 112, 141, 28, 96, 132, 119, 35, 7, 216, 19, 94, 227, 6, 24, 169, 224, 198, 13, 176, 167, 132, 78, 13, 48, 83, 195, 157, 26, 224, 153, 34, 58, 51, 192, 76, 21, 119, 102, 128, 103, 202, 232, 196, 0, 51, 117, 220, 137, 1, 158, 41, 164, 175, 31, 96, 166, 146, 251, 250, 1, 158, 41, 165, 175, 30, 96, 166, 150, 251, 234, 1, 158, 41, 166, 175, 29, 96, 166, 154, 251, 218, 1, 158, 41, 167, 175, 28, 96, 166, 158, 251, 202, 1, 158, 41, 168, 175, 27, 96, 166, 162, 251, 186, 1, 158, 41, 169, 175, 26, 96, 166, 166, 251, 170, 1, 158, 41, 170, 175, 25, 96, 166, 170, 251, 154, 1, 158, 41, 171, 175, 24, 96, 166, 174, 251, 138, 1, 158, 41, 172, 191, 126, 128, 153, 202, 238, 175, 31, 224, 153, 210, 126, 203, 139, 253, 145, 218, 190, 203, 139, 125, 136, 36, 73, 146, 36, 73, 146, 36, 73, 146, 36, 73, 146, 36, 73, 146, 36, 73, 146, 36, 73, 146, 36, 73, 146, 36, 73, 146, 36, 73, 146, 36, 73, 146, 36, 73, 146, 36, 73, 146, 36, 73, 146, 36, 73, 146, 222, 167, 127, 0, 118, 253, 246, 211, 118, 140, 32, 233, 0, 0, 0, 0, 73, 69, 78, 68, 174, 66, 96, 130}},
		
		Release: false,
		Custom: map[string]string{
			
		},
		
	})
}

